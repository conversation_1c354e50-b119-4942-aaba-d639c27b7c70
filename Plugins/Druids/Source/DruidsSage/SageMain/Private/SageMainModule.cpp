#include "SageMainModule.h"

#include "ChatRequestHandler_V2.h"
#include "DruidsSageEditorModule.h"
#include "SageExtensionDelegator.h"
#include "SDruidsSageChatShell.h"
#include "ChatWidgetOverrides.h"

// UMG includes
#include "Blueprint/UserWidget.h"
#include "Engine/World.h"
#include "Editor.h"

// Asset loading includes
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "UObject/ConstructorHelpers.h"
#include "Engine/Engine.h"

#define LOCTEXT_NAMESPACE "FSageMainModule"

void FSageMainModule::StartupModule()
{
	// Register for post-engine init callback
	FCoreDelegates::OnPostEngineInit.AddRaw(this, &FSageMainModule::OnPostEngineInit);
}

void FSageMainModule::ShutdownModule()
{
	if (FModuleManager::Get().IsModuleLoaded("DruidsSageEditorModule"))
	{
		FDruidsSageEditorModule& EditorModule = FModuleManager::GetModuleChecked<FDruidsSageEditorModule>("DruidsSageEditorModule");
		EditorModule.OnCreateChatShell.Unbind();
	}

	// Clear cached ChatWidgetOverrides instance
	CachedChatWidgetOverrides = nullptr;

	FCoreDelegates::OnPostEngineInit.RemoveAll(this);
}

void FSageMainModule::OnPostEngineInit()
{
	if (FModuleManager::Get().IsModuleLoaded("DruidsSageEditorModule"))
	{
		FDruidsSageEditorModule& EditorModule = FModuleManager::GetModuleChecked<FDruidsSageEditorModule>("DruidsSageEditorModule");
        
		// Bind a lambda to the OnCreateChatShell event
		EditorModule.OnCreateChatShell.BindLambda([this]() -> TObjectPtr<UDruidsSageChatShell> {
			// Create the UMG widget - we need a world context for CreateWidget
			UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
			if (!World)
			{
				return nullptr;
			}

			// Try to load ChatWidgetOverrides to get the custom widget class
			UChatWidgetOverrides* WidgetOverrides = LoadChatWidgetOverrides();
			TSubclassOf<UDruidsSageChatShell> WidgetClass = UDruidsSageChatShell::StaticClass(); // Default fallback

			if (WidgetOverrides)
			{
				if (TSubclassOf<UDruidsSageChatShell> OverrideClass = WidgetOverrides->GetChatShellWidgetClass())
				{
					WidgetClass = OverrideClass;
					UE_LOG(LogTemp, Log, TEXT("Using custom ChatShell widget class: %s"), *WidgetClass->GetName());
				}
				else
				{
					UE_LOG(LogTemp, Log, TEXT("ChatWidgetOverrides found but no custom class specified, using default"));
				}
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("No ChatWidgetOverrides found, using default UDruidsSageChatShell"));
			}

			// Create the widget using the determined class
			UDruidsSageChatShell* ChatShell = CreateWidget<UDruidsSageChatShell>(World, WidgetClass);
			if (ChatShell)
			{
				ChatShell->SetChatRequestHandler(MakeShared<ChatRequestHandler_V2>());
				ChatShell->SetExtensionDelegator(MakeShared<FSageExtensionDelegator>());
			}

			return ChatShell;
		});
	}
}

UChatWidgetOverrides* FSageMainModule::LoadChatWidgetOverrides()
{
	// Return cached instance if already loaded
	if (CachedChatWidgetOverrides)
	{
		return CachedChatWidgetOverrides;
	}

	// Hard-coded path to the ChatWidgetOverrides Blueprint in the plugin's Content folder
	const FString BlueprintPath = TEXT("/Druids/ChatWidgets/ChatWidgetOverrides.ChatWidgetOverrides_C");

	// Try to load the Blueprint class
	UClass* BlueprintClass = LoadClass<UChatWidgetOverrides>(nullptr, *BlueprintPath);
	if (!BlueprintClass)
	{
		// Blueprint not found or failed to load
		UE_LOG(LogTemp, Warning, TEXT("ChatWidgetOverrides Blueprint not found at path: %s"), *BlueprintPath);
		return nullptr;
	}

	// Create an instance of the Blueprint class using the transient package as outer
	if (GEditor)
	{
		UWorld* EditorWorld = GEditor->GetEditorWorldContext().World();
		CachedChatWidgetOverrides = NewObject<UChatWidgetOverrides>(EditorWorld, BlueprintClass);
	}

	if (!CachedChatWidgetOverrides)
	{
		CachedChatWidgetOverrides = NewObject<UChatWidgetOverrides>(GetTransientPackage(), BlueprintClass);
	}
	
	if (!CachedChatWidgetOverrides)
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to create instance of ChatWidgetOverrides Blueprint"));
		return nullptr;
	}

	UE_LOG(LogTemp, Log, TEXT("Successfully loaded ChatWidgetOverrides from: %s"), *BlueprintPath);
	return CachedChatWidgetOverrides;
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FSageMainModule, SageMain)